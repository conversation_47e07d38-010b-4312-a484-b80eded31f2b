package cn.coder.zj.module.collector.collect.basicdata.manager.zstack;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.collect.token.zstack.ZStackClientWrapper;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.CommonUtil;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.coder.zj.module.collector.util.StringUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VmData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.core.task.TaskExecutor;
import org.zstack.sdk.*;
import org.zstack.sdk.zwatch.api.GetMetricDataAction;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

import static cn.coder.zj.module.collector.util.CommonUtil.getBigFromJson;
import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VM;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_ZS_TACK_VM;
import static java.util.Arrays.asList;

@Slf4j
public class ZsTackBasicVmDataImpl extends AbstractBasicData {

    protected long startTime;

    private static final BigDecimal ZERO = new BigDecimal(0);
    private static final String KVM = "KVM";
    private static final String VM_UUID_PREFIX = "VMUuid=";

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.ZS_TACK.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            if (platform.getState() == 1) {
                continue;
            }
            // 创建 final 引用，确保异步线程中使用正确的平台对象
            final Platform currentPlatform = platform;
            taskExecutor.execute(() -> {
                try {
                    List<VmData> data = collectData(currentPlatform);
                    message.setType(ClusterMsg.MessageType.BASIC);
                    message.setData(GsonUtil.GSON.toJson(BasicCollectData.builder().basicDataMap(data).metricsName(BASIC_VM.code()).build()));
                    message.setTime(System.currentTimeMillis());
                    sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                    String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                    log.info("平台 {} 虚拟机数据收集完成，耗时 {} 秒", platform.getPlatformName(), endTimeFormatted);
                } catch (Exception e) {
                    log.error("平台 {} 虚拟机数据收集失败: {}", platform.getPlatformName(), e.getMessage(), e);
                    // 如果是认证相关错误，可以考虑重试或标记平台状态
                    handleCollectionError(platform, e);
                }
            });
        }
    }

    private List<VmData> collectData(Platform platform) {
        List<VmData> vmDataList = Lists.newArrayList();

        // 使用线程安全的ZStack API调用查询区域列表
        QueryZoneAction.Result zoneRes = ZStackClientWrapper.queryZonesAsync(platform);
        List<?> regionList = zoneRes.value.inventories;

        // 使用线程安全的虚拟机查询
        QueryVmInstanceAction.Result res = ZStackClientWrapper.queryVmInstancesAsync(platform);
        if (res != null && res.value != null && CollUtil.isNotEmpty(res.value.inventories)) {
            for (Object vm : res.value.inventories) {
                JsonObject jsonObject = GsonUtil.GSON.toJsonTree(vm).getAsJsonObject();
                JsonElement hypervisorTypeElement = jsonObject.get("hypervisorType");
                String hypervisorType = hypervisorTypeElement != null && !hypervisorTypeElement.isJsonNull() ?
                        hypervisorTypeElement.getAsString() : "";
                String uuid = getStringFromJson(jsonObject, "uuid", "");
                if (uuid != null && !uuid.isEmpty() && KVM.equals(hypervisorType)) {
                    processVmData(jsonObject, platform, regionList, vmDataList);
                }
            }
        }

        return vmDataList;
    }

    private void processVmData(JsonObject jsonObject, Platform platform, List regionList, List<VmData> vmDataList) {
        try {
            VmData vmData = new VmData();
            AtomicReference<String> clusterName = new AtomicReference<>("");
            AtomicReference<String> hardwareName = new AtomicReference<>("");
            // 获取基本信息
            String uuid = getStringFromJson(jsonObject, "uuid", "");
            String name = getStringFromJson(jsonObject, "name", "-");
            String state = getStringFromJson(jsonObject, "state", "-");
            String imageUuid = getStringFromJson(jsonObject, "imageUuid", "");
            String clusterUuid = getStringFromJson(jsonObject, "clusterUuid", "");
            String hardwareUuid = getStringFromJson(jsonObject, "lastHostUuid", "");
            String zoneUuid = getStringFromJson(jsonObject, "zoneUuid", "");
            String type = getStringFromJson(jsonObject, "type", "-");
            Long memorySize = getLongFromJson(jsonObject, "memorySize");
            Integer cpuNum = getIntFromJson(jsonObject, "cpuNum");
            // 默认值设置
            String ip = "";
            String vip_ip = "";
            String mac = "";
            String architecture = jsonObject.get("architecture").getAsString();
            String guestOsType = jsonObject.get("guestOsType").getAsString();
            String zoneName = "";
            // 创建日期处理
            String createDateStr = getStringFromJson(jsonObject, "createDate", "");
            Date createDate = new Date();
            if (!createDateStr.isEmpty()) {
                createDate = new Date(createDateStr);
            }
            // 使用线程安全的指标数据获取方法
            BigDecimal cpu_useds = ZStackClientWrapper.getVmMetricDataAsync(platform, "CPUAverageUsedUtilization", uuid) != null ?
                    getMetricValue(ZStackClientWrapper.getVmMetricDataAsync(platform, "CPUAverageUsedUtilization", uuid)) : ZERO;
            BigDecimal memory_useds = ZStackClientWrapper.getVmMetricDataAsync(platform, "MemoryUsedInPercent", uuid) != null ?
                    getMetricValue(ZStackClientWrapper.getVmMetricDataAsync(platform, "MemoryUsedInPercent", uuid)) : ZERO;
            BigDecimal inBytes = ZStackClientWrapper.getVmMetricDataAsync(platform, "NetworkAllInBytes", uuid) != null ?
                    getMetricValue(ZStackClientWrapper.getVmMetricDataAsync(platform, "NetworkAllInBytes", uuid)) : ZERO;
            BigDecimal outBytes = ZStackClientWrapper.getVmMetricDataAsync(platform, "NetworkAllOutBytes", uuid) != null ?
                    getMetricValue(ZStackClientWrapper.getVmMetricDataAsync(platform, "NetworkAllOutBytes", uuid)) : ZERO;
            BigDecimal inPackets = ZStackClientWrapper.getVmMetricDataAsync(platform, "NetworkAllInPackets", uuid) != null ?
                    getMetricValue(ZStackClientWrapper.getVmMetricDataAsync(platform, "NetworkAllInPackets", uuid)) : ZERO;
            BigDecimal outPackets = ZStackClientWrapper.getVmMetricDataAsync(platform, "NetworkAllOutPackets", uuid) != null ?
                    getMetricValue(ZStackClientWrapper.getVmMetricDataAsync(platform, "NetworkAllOutPackets", uuid)) : ZERO;
            // 磁盘相关数据初始化
            BigDecimal actualSize = ZERO;
            BigDecimal cloudSize = ZERO;
            BigDecimal diskFreeBytes = ZERO;
            BigDecimal diskUsed = ZERO;
            // 处理云盘数据
            JsonArray jsonArrays = jsonObject.getAsJsonArray("allVolumes");
            if (jsonArrays != null && !jsonArrays.isEmpty()) {
                for (int i = 0; i < jsonArrays.size(); i++) {
                    JsonObject volumeObj = jsonArrays.get(i).getAsJsonObject();
                    // 处理Root盘
                    if ("Root".equals(getStringFromJson(volumeObj, "type", ""))) {
                        actualSize = getBigDecimalFromJson(volumeObj, "actualSize", ZERO);
                        cloudSize = getBigDecimalFromJson(volumeObj, "size", ZERO);
                        diskFreeBytes = cloudSize.subtract(actualSize);
                        diskUsed = actualSize.divide(cloudSize,2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP);
                    }
                }
            }
            // 获取区域名称
            for (Object item : regionList) {
                if (item instanceof ZoneInventory) {
                    ZoneInventory zone = (ZoneInventory) item;
                    if (zone.getUuid().equals(zoneUuid)) {
                        zoneName = zone.getName();
                        break;
                    }
                }
            }
            // 使用线程安全的集群查询
            QueryClusterAction.Result clusterResult = ZStackClientWrapper.queryClustersWithConditionsAsync(platform, List.of("hypervisorType=" + KVM));
            if (clusterResult != null && clusterResult.value != null &&
                    CollUtil.isNotEmpty(clusterResult.value.inventories)) {
                for (Object cluster : clusterResult.value.inventories) {
                    JsonObject clusterObj = GsonUtil.GSON.toJsonTree(cluster).getAsJsonObject();
                    if (clusterUuid.equals(getStringFromJson(clusterObj, "uuid", ""))) {
                        clusterName.set(getStringFromJson(clusterObj, "name", ""));
                        break;
                    }
                }
            }
            // 使用线程安全的主机查询
            QueryHostAction.Result hostResult = ZStackClientWrapper.queryHostsAsync(platform);
            if (hostResult != null && hostResult.value != null &&
                    CollUtil.isNotEmpty(hostResult.value.inventories)) {
                for (Object host : hostResult.value.inventories) {
                    JsonObject hostObj = GsonUtil.GSON.toJsonTree(host).getAsJsonObject();
                    if (hardwareUuid.equals(getStringFromJson(hostObj, "uuid", ""))) {
                        hardwareName.set(getStringFromJson(hostObj, "name", ""));
                        break;
                    }
                }
            }
            // 使用线程安全的EIP查询
            QueryEipAction.Result eipResult = ZStackClientWrapper.executeWithClientAsync(platform, () -> {
                QueryEipAction action = new QueryEipAction();
                ZStackClientWrapper.setAuthentication(action, platform);
                return action.call();
            });

            // 弹性ip
            JsonArray jsonArray = jsonObject.getAsJsonArray("vmNics");
            if (!jsonArray.isEmpty()) {
                List<String> ipList = new ArrayList<>();
                List<String> macList = new ArrayList<>();
                for (int i = 0; i < jsonArray.size(); i++) {
                    JsonObject object = jsonArray.get(i).getAsJsonObject();
                    JsonElement macElement = object.get("mac");
                    JsonElement ipElement = object.get("ip");
                    if (macElement != null && !macElement.isJsonNull()) {
                        macList.add(macElement.getAsString());
                    }
                    if (ipElement != null && !ipElement.isJsonNull()) {
                        ipList.add(ipElement.getAsString());
                    }
                }
                ip = CollUtil.join(ipList, ",");
                mac = CollUtil.join(macList, ",");
                if (ip.equals("null")) {
                    ip = "";
                }
                for (int i = 0; i < jsonArray.size(); i++) {
                    JsonObject vm = jsonArray.get(i).getAsJsonObject();
                    if (eipResult != null && eipResult.value != null && CollUtil.isNotEmpty(eipResult.value.inventories)) {
                        for (Object eip : eipResult.value.inventories) {
                            JsonObject jsonObject1 = GsonUtil.GSON.toJsonTree(eip).getAsJsonObject();
                            String eipUuid = jsonObject1.get("vmNicUuid").getAsString();
                            if (eipUuid.equals(vm.get("uuid").getAsString())) {
                                vip_ip = jsonObject1.get("vipIp").getAsString();
                            }
                        }
                    }
                }
            }
            //电源状态
            String powerState = switch (state) {
                case "Created" -> "on";
                case "Starting" -> "on";
                case "Running" -> "on";
                case "Stopping" -> "off";
                case "Stopped" -> "off";
                case "Unknown" -> "unknown";
                case "Rebooting" -> "on";
                case "Destroyed" -> "off";
                case "Destroying" -> "off";
                case "Migrating" -> "on";
                case "Expunging" -> "off";
                case "Paunging" -> "off";
                case "Paused" -> "off";
                case "Resuming" -> "on";
                case "VolumeMigrating" -> "on";
                default -> "unknown";
            };

            // 使用线程安全的Guest Tools查询
            GetVmGuestToolsInfoAction.Result res = ZStackClientWrapper.getVmGuestToolsInfoWithUuidAsync(platform, uuid);
            if(res.value != null){
                boolean toolsRunning = "Running".equals(res.value.status);
                vmData.setToolsInstalled(String.valueOf(toolsRunning));
                vmData.setToolsRunStatus(toolsRunning ? "run" : "stop");

                if (toolsRunning) {
                    // 使用线程安全的磁盘使用率查询
                    GetMetricDataAction.Result result = ZStackClientWrapper.executeWithClientAsync(platform, () -> {
                        GetMetricDataAction action = new GetMetricDataAction();
                        action.namespace = "ZStack/VM";
                        action.metricName = "DiskUsedCapacityInPercent";
                        action.labels = List.of("VMUuid=" + uuid);
                        long now = System.currentTimeMillis() / 1000;
                        action.startTime = now - 60;
                        action.endTime = now;
                        ZStackClientWrapper.setAuthentication(action, platform);
                        return action.call();
                    });

                    if (result != null && result.value != null && CollUtil.isNotEmpty(result.value.data)) {
                        JsonArray metricArray = GsonUtil.GSON.toJsonTree(result.value.data).getAsJsonArray();
                        for (int i = metricArray.size() - 1; i >= 0; i--) {
                            JsonObject entry = metricArray.get(i).getAsJsonObject();
                            JsonObject labels = entry.getAsJsonObject("labels");
                            boolean isRootFs = "/".equals(getStringFromJson(labels, "MountPoint", ""))
                                    && "rootfs".equals(getStringFromJson(labels, "DiskDeviceLetter", ""));
                            if (isRootFs) {
                                diskUsed = getBigFromJson(entry, "value");
                                break;
                            }
                        }
                    }
                }
            }

            // 使用线程安全的用户标签查询
            QueryUserTagAction.Result restag = ZStackClientWrapper.executeWithClientAsync(platform, () -> {
                QueryUserTagAction action = new QueryUserTagAction();
                action.conditions = asList("resourceUuid=" + uuid,"resourceType=VmInstanceVO");
                ZStackClientWrapper.setAuthentication(action, platform);
                return action.call();
            });

            List<String> tagValues = new ArrayList<>();
            for (Object inventory : restag.value.inventories) {
                JsonObject tagDO = GsonUtil.GSON.toJsonTree(inventory).getAsJsonObject().getAsJsonObject("tagPattern");
                String tag = getStringFromJson(tagDO, "name", "");
                String taguuid = getStringFromJson(tagDO, "uuid", "");
                if(StrUtil.isNotEmpty(tag) && StrUtil.isNotEmpty(taguuid)){
                    tagValues.add(tag +"&"+taguuid);
                }
            }

            String resultTag = tagValues.isEmpty() ? "" :(tagValues.size() == 1 ? tagValues.get(0) : String.join(",", tagValues));

            // 设置VmData属性
            vmData.setPowerState(powerState);
            vmData.setDeleted(0);
            vmData.setUuid(uuid);
            vmData.setName(name);
            vmData.setState(state);
            vmData.setIp(ip);
            vmData.setVipIp(vip_ip);
            vmData.setZoneUuid(zoneUuid);
            vmData.setClusterUuid(clusterUuid);
            vmData.setImageUuid(imageUuid);
            vmData.setHardwareUuid(hardwareUuid);
            vmData.setArchitecture(architecture);
            vmData.setGuestOsType(guestOsType);
            vmData.setVCreateDate(DateUtil.date(createDate));
            vmData.setCpuUsed(cpu_useds);
            vmData.setMemoryUsed(memory_useds);
            vmData.setZoneName(zoneName);
            vmData.setClusterName(clusterName.toString());
            vmData.setHardwareName(hardwareName.toString());
            vmData.setType(type);
            vmData.setMemorySize(StringUtil.toLong(memorySize));
            vmData.setCpuNum(StringUtil.toInt(cpuNum));
            vmData.setDiskUsed(diskUsed);
            vmData.setTag(resultTag);
            // 处理MAC地址
            String trimmedMac = StringUtil.toString(mac);
            if (trimmedMac == null || trimmedMac.isEmpty()) {
                vmData.setMac("-");
            } else {
                if (trimmedMac.contains(",")) {
                    // 如果包含逗号，取第一个MAC地址
                    vmData.setMac(trimmedMac.split(",")[0].trim());
                } else {
                    // 如果不包含逗号，直接返回
                    vmData.setMac(trimmedMac);
                }
            }
            // 设置平台相关信息
            vmData.setRegionId(platform.getRegionId());
            vmData.setPlatformId(platform.getPlatformId());
            vmData.setPlatformName(platform.getPlatformName());
            // 设置磁盘相关信息
            vmData.setActualSize(actualSize);
            vmData.setCloudSize(cloudSize);
            vmData.setNetworkInBytes(inBytes);
            vmData.setNetworkOutBytes(outBytes);
            vmData.setDiskUsedBytes(actualSize);
            vmData.setNetworkInPackets(inPackets);
            vmData.setNetworkOutPackets(outPackets);
            vmData.setDiskFreeBytes(diskFreeBytes);
            vmData.setTotalDiskCapacity(cloudSize);
            vmData.setTypeName("zstack");
            // 添加到列表
            vmDataList.add(vmData);
        } catch (Exception e) {
            log.error("处理虚拟机数据时发生错误: {}", CommonUtil.getMessageFromThrowable(e));
        }
    }

    /**
     * 从JsonObject中安全获取BigDecimal值
     *
     * @param jsonObject   JSON对象
     * @param key          键
     * @param defaultValue 默认值
     * @return BigDecimal值
     */
    private BigDecimal getBigDecimalFromJson(JsonObject jsonObject, String key, BigDecimal defaultValue) {
        JsonElement element = jsonObject.get(key);
        if (element != null && !element.isJsonNull()) {
            return element.getAsBigDecimal();
        }
        return defaultValue;
    }

    /**
     * 从指标查询结果中提取数值
     *
     * @param result 指标查询结果
     * @return 指标值
     */
    private BigDecimal getMetricValue(GetMetricDataAction.Result result) {
        try {
            if (result != null && result.value != null && CollUtil.isNotEmpty(result.value.data)) {
                JsonObject dataObj = GsonUtil.GSON.toJsonTree(result.value.data.get(0)).getAsJsonObject();
                return dataObj.get("value").getAsBigDecimal();
            }
        } catch (Exception e) {
            log.error("解析指标数据失败: {}", e.getMessage());
        }
        return ZERO;
    }

    /**
     * 处理数据收集错误
     */
    private void handleCollectionError(Platform platform, Exception e) {
        String errorMessage = e.getMessage();

        if (errorMessage != null && (errorMessage.contains("session expired") ||
            errorMessage.contains("does not existed or disabled") ||
            errorMessage.contains("Token为空或无效"))) {

            log.warn("平台 {} 认证失败，可能需要重新获取认证信息: {}", platform.getPlatformName(), errorMessage);
            // 这里可以添加重新获取认证信息的逻辑
            // 或者标记平台为需要重新认证状态

        } else if (errorMessage != null && errorMessage.contains("网络不可达")) {
            log.warn("平台 {} 网络连接失败: {}", platform.getPlatformName(), errorMessage);
            // 可以标记平台为离线状态

        } else {
            log.error("平台 {} 数据收集发生未知错误: {}", platform.getPlatformName(), errorMessage);
        }
    }





    /**
     * 从JsonObject中安全获取长整型值
     *
     * @param jsonObject JSON对象
     * @param key        键
     * @return 长整型值
     */
    private Long getLongFromJson(JsonObject jsonObject, String key) {
        JsonElement element = jsonObject.get(key);
        if (element != null && !element.isJsonNull()) {
            return element.getAsLong();
        }
        return 0L;
    }

    /**
     * 从JsonObject中安全获取整型值
     *
     * @param jsonObject JSON对象
     * @param key        键
     * @return 整型值
     */
    private Integer getIntFromJson(JsonObject jsonObject, String key) {
        JsonElement element = jsonObject.get(key);
        if (element != null && !element.isJsonNull()) {
            return element.getAsInt();
        }
        return 0;
    }

    @Override
    public String supportProtocol() {
        return BASIC_ZS_TACK_VM.code();
    }
}
