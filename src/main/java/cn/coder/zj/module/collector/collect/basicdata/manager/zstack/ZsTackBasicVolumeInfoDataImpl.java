package cn.coder.zj.module.collector.collect.basicdata.manager.zstack;

import cn.coder.zj.module.collector.collect.basicdata.AbstractBasicData;
import cn.coder.zj.module.collector.collect.token.zstack.ZStackClientWrapper;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.GsonUtil;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.zj.framework.common.dal.manager.BasicCollectData;
import cn.iocoder.zj.framework.common.dal.manager.VolumeInfoData;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.zstack.sdk.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.iocoder.zj.framework.common.enums.BasicServiceType.BASIC_VOLUME_INFO;
import static cn.iocoder.zj.framework.common.enums.BasicType.BASIC_ZS_TACK_VOLUME_INFO;
import static java.util.Arrays.asList;

@Slf4j
public class ZsTackBasicVolumeInfoDataImpl extends AbstractBasicData {
    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectBasicData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.ZS_TACK.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);
        for (Object platformObj : platformList) {
            Platform platform = (Platform) platformObj;
            if (platform.getState() == 1) {
                continue;
            }
            // 创建 final 引用，确保异步线程中使用正确的平台对象
            final Platform currentPlatform = platform;
            taskExecutor.execute(() -> {
                List<VolumeInfoData> volumeInfoData = new ArrayList<>();
                collectData(currentPlatform, volumeInfoData);

                if (!volumeInfoData.isEmpty()) {
                    BasicCollectData build = BasicCollectData.builder().basicDataMap(volumeInfoData)
                            .metricsType("")
                            .metricsName(BASIC_VOLUME_INFO.code())
                            .build();
                    String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                    log.info("collect basic data end, cost {} seconds", endTimeFormatted);
                    message.setType(ClusterMsg.MessageType.BASIC);
                    message.setData(GsonUtil.GSON.toJson(build));
                    message.setTime(System.currentTimeMillis());
                    sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                }
            });
        }

    }

    private void collectData(Platform platform, List<VolumeInfoData> volumeInfoDataList) {
        // 使用线程安全的存储卷查询
        QueryVolumeAction.Result res = ZStackClientWrapper.queryVolumesAsync(platform);

        // 使用线程安全的虚拟机查询
        QueryVmInstanceAction.Result vmResult = ZStackClientWrapper.queryVmInstancesAsync(platform);
        Map<String,String> vmUuidName = new HashMap<>();
        for (Object vmo : vmResult.value.inventories) {
            VmInstanceInventory vmoO = (VmInstanceInventory) vmo;
            vmUuidName.put(vmoO.getUuid(),vmoO.getName());
        }

        // 使用线程安全的主存储查询
        QueryPrimaryStorageAction.Result resPriStorage = ZStackClientWrapper.queryPrimaryStoragesAsync(platform);
        Map<String,PrimaryStorageInventory> storageUuidName = new HashMap<>();
        for (Object vmo : resPriStorage.value.inventories) {
            PrimaryStorageInventory vmoO = (PrimaryStorageInventory) vmo;
            storageUuidName.put(vmoO.getUuid(),vmoO);
        }

        if (res != null && res.value != null && CollUtil.isNotEmpty(res.value.inventories)) {
            JsonArray asJsonArray = GsonUtil.GSON.toJsonTree(res.value.inventories).getAsJsonArray();
            asJsonArray.forEach(jsonElement -> {
                JsonObject jsonObject = jsonElement.getAsJsonObject();
                String uuid = jsonObject.get("uuid").getAsString();
                String platformName = platform.getPlatformName();
                String name = jsonObject.get("name").getAsString();
                // 处理description字段可能为空的情况
                String description = jsonObject.has("description") && !jsonObject.get("description").isJsonNull()
                        ? jsonObject.get("description").getAsString()
                        : "";
                String primaryStorageUuid = jsonObject.get("primaryStorageUuid").getAsString();
                String primaryStorageName = "";
                String primaryStorageType = "";
                if (!Objects.equals(primaryStorageUuid, "") && storageUuidName.get(primaryStorageUuid) != null) {
                    primaryStorageName = storageUuidName.get(primaryStorageUuid).getName();
                }
                if (!Objects.equals(primaryStorageUuid, "") && storageUuidName.get(primaryStorageUuid) != null) {
                    primaryStorageType = storageUuidName.get(primaryStorageUuid).getType().toLowerCase();
                }
                String vminstanceUuid = jsonObject.get("vmInstanceUuid").getAsString();
                String vmInstanceName = "";
                if (!Objects.equals(vminstanceUuid, "") && vmUuidName.get(vminstanceUuid) != null) {
                    vmInstanceName = vmUuidName.get(vminstanceUuid);
                }
                String type = jsonObject.get("type").getAsString();
                String format = jsonObject.get("format").getAsString();
                Long size = jsonObject.get("size").getAsLong();
                Long actualSize = jsonObject.get("actualSize").getAsLong();
                String state = jsonObject.get("state").getAsString();
                String status = jsonObject.get("status").getAsString();
                Date vCreateTime = new Date(jsonObject.get("createDate").getAsString());
                Date vUpdateTime = new Date(jsonObject.get("lastOpDate").getAsString());
                Long actualFree = size - actualSize > 0 ? size - actualSize : 0;
                BigDecimal result = BigDecimal.valueOf(actualSize).divide(BigDecimal.valueOf(size), 2, RoundingMode.HALF_UP);


                QueryUserTagAction actionTag = new QueryUserTagAction();
                actionTag.conditions = asList("resourceUuid=" + uuid,"resourceType=VolumeVO");
                if (platform.getAkType()==0){
                    actionTag.sessionId = platform.getZsTackPlatform().getToken();
                }else {
                    actionTag.accessKeyId = platform.getUsername();
                    actionTag.accessKeySecret = platform.getPassword();
                }
                QueryUserTagAction.Result restag = actionTag.call();

                List<String> tagValues = new ArrayList<>();
                for (Object inventory : restag.value.inventories) {
                    JsonObject tagDO = GsonUtil.GSON.toJsonTree(inventory).getAsJsonObject().getAsJsonObject("tagPattern");
                    String tag = getStringFromJson(tagDO, "name", "");
                    String taguuid = getStringFromJson(tagDO, "uuid", "");
                    if(StrUtil.isNotEmpty(tag) && StrUtil.isNotEmpty(taguuid)){
                        tagValues.add(tag +"&"+taguuid);
                    }
                }

                String resultTag = tagValues.isEmpty() ? "" :(tagValues.size() == 1 ? tagValues.get(0) : String.join(",", tagValues));

                volumeInfoDataList.add(
                        VolumeInfoData.builder().uuid(uuid)
                                .platformId(platform.getPlatformId().toString())
                                .platformName(platformName)
                                .name(name)
                                .description(description)
                                .primaryStorageUuid(primaryStorageUuid)
                                .primaryStorageName(primaryStorageName)
                                .primaryStorageType(primaryStorageType)
                                .vmInstanceUuid(vminstanceUuid)
                                .vmInstanceName(vmInstanceName)
                                .type(type)
                                .format(format)
                                .size(size)
                                .actualSize(actualSize)
                                .actualUse(actualSize)
                                .actualFree(actualFree)
                                .state(state)
                                .status(status)
                                .actualRatio(result.compareTo(BigDecimal.valueOf(1)) > 0 ? "1.00" : result.toString())
                                .deleted(0)
                                .vCreateDate(vCreateTime)
                                .vUpdateDate(vUpdateTime)
                                .mediaType("rotate")
                                .isMount(vminstanceUuid != null)
                                .vCreateDate(new Date(jsonObject.get("createDate").getAsString()))
                                .tag(resultTag)
                                .build());

            });
        }
    }

    @Override
    public String supportProtocol() {
        return BASIC_ZS_TACK_VOLUME_INFO.code();
    }
}
